# Quick Compatibility Test for Critical Issues
# This script tests the most important compatibility issues found

param(
    [switch]$Verbose = $false
)

$ScoPath = ".\Build\msvc-debug\sco.exe"

function Test-Command {
    param(
        [string]$TestName,
        [string]$Command,
        [string]$ExpectedPattern = "",
        [string]$Description = ""
    )
    
    Write-Host "`n[$TestName] $Description" -ForegroundColor Yellow
    Write-Host "Command: $Command" -ForegroundColor Gray
    
    try {
        $output = Invoke-Expression $Command 2>&1
        $exitCode = $LASTEXITCODE
        
        Write-Host "Exit Code: $exitCode" -ForegroundColor Gray
        Write-Host "Output:" -ForegroundColor Gray
        Write-Host $output
        
        if ($ExpectedPattern -and $output -match $ExpectedPattern) {
            Write-Host "✓ PASS - Expected pattern found" -ForegroundColor Green
            return $true
        } elseif ($ExpectedPattern -and $output -notmatch $ExpectedPattern) {
            Write-Host "✗ FAIL - Expected pattern not found" -ForegroundColor Red
            Write-Host "Expected pattern: $ExpectedPattern" -ForegroundColor Red
            return $false
        } else {
            Write-Host "ℹ INFO - Manual verification needed" -ForegroundColor Blue
            return $null
        }
    } catch {
        Write-Host "✗ ERROR - Command failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

Write-Host "SCO Critical Issues Quick Test" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

# Check if SCO exists
if (-not (Test-Path $ScoPath)) {
    Write-Host "✗ SCO not found at $ScoPath" -ForegroundColor Red
    Write-Host "Please build SCO first" -ForegroundColor Red
    exit 1
}

Write-Host "✓ SCO found at $ScoPath" -ForegroundColor Green

$results = @{}

# Test 1: Basic functionality (should work)
Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "Testing Basic Functionality (Should Work)" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

$results["version"] = Test-Command "VERSION" "$ScoPath --version" "0\.1\.0" "Version command"
$results["help"] = Test-Command "HELP" "$ScoPath help" "Available commands" "Help command"
$results["list"] = Test-Command "LIST" "$ScoPath list" "Installed apps" "List command"

# Test 2: Critical issues (currently broken)
Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "Testing Critical Issues (Currently Broken)" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

$results["bucket_list"] = Test-Command "BUCKET_LIST" "$ScoPath bucket list" "main.*extras.*spc" "Bucket list should show buckets, not apps"
$results["shim_list"] = Test-Command "SHIM_LIST" "$ScoPath shim list" "aria2c.*scoop" "Shim list should show shims, not apps"
$results["info_git"] = Test-Command "INFO_GIT" "$ScoPath info git" "Distributed version control" "Info git should work without JSON errors"

# Test 3: Working info command (should work)
Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "Testing Working Commands" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

$results["info_7zip"] = Test-Command "INFO_7ZIP" "$ScoPath info 7zip" "file archiver" "Info 7zip should work"
$results["cat_git"] = Test-Command "CAT_GIT" "$ScoPath cat git" "version.*description" "Cat git should work"
$results["search"] = Test-Command "SEARCH" "$ScoPath search git" "Found.*app.*matching" "Search should work"

# Summary
Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "TEST SUMMARY" -ForegroundColor Yellow
Write-Host "="*60 -ForegroundColor Cyan

$passed = 0
$failed = 0
$manual = 0

foreach ($test in $results.Keys) {
    $result = $results[$test]
    $status = switch ($result) {
        $true { $passed++; "✓ PASS" }
        $false { $failed++; "✗ FAIL" }
        $null { $manual++; "? MANUAL" }
    }
    
    $color = switch ($result) {
        $true { "Green" }
        $false { "Red" }
        $null { "Yellow" }
    }
    
    Write-Host "$($test.ToUpper().PadRight(15)) $status" -ForegroundColor $color
}

Write-Host "`nResults:" -ForegroundColor White
Write-Host "  Passed: $passed" -ForegroundColor Green
Write-Host "  Failed: $failed" -ForegroundColor Red
Write-Host "  Manual: $manual" -ForegroundColor Yellow
Write-Host "  Total:  $($passed + $failed + $manual)" -ForegroundColor White

# Critical issues check
$criticalIssues = @("bucket_list", "shim_list", "info_git")
$criticalFailed = 0

foreach ($issue in $criticalIssues) {
    if ($results[$issue] -eq $false) {
        $criticalFailed++
    }
}

Write-Host "`nCritical Issues Status:" -ForegroundColor White
if ($criticalFailed -eq 0) {
    Write-Host "✓ All critical issues resolved!" -ForegroundColor Green
} else {
    Write-Host "✗ $criticalFailed critical issues remain" -ForegroundColor Red
    Write-Host "  These must be fixed before release" -ForegroundColor Red
}

# Recommendations
Write-Host "`nRecommendations:" -ForegroundColor White
if ($results["bucket_list"] -eq $false) {
    Write-Host "  • Fix bucket command routing in command_manager.hpp" -ForegroundColor Yellow
}
if ($results["shim_list"] -eq $false) {
    Write-Host "  • Fix shim command routing in command_manager.hpp" -ForegroundColor Yellow
}
if ($results["info_git"] -eq $false) {
    Write-Host "  • Fix JSON parser to handle array-type fields in manifests" -ForegroundColor Yellow
}

Write-Host "`nFor detailed analysis, see:" -ForegroundColor Gray
Write-Host "  • test_results_summary.md" -ForegroundColor Gray
Write-Host "  • compatibility_issues.md" -ForegroundColor Gray
